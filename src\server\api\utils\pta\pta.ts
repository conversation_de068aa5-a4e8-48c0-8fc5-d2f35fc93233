import { TRPCError } from "@trpc/server";
import type { Context } from "../../trpc";
import { env } from "~/env";
import type { PTAAthlete, PTACompetition } from "~/lib/interfaces/externalCall";
import { unstable_cache } from "next/cache";
import { getServiceToken } from "../service";

export const getAccessToken = (ctx: Context) => {
  if (!ctx.session?.accessToken) {
    throw new TRPCError({
      code: "UNAUTHORIZED",
      message: "User is not authenticated",
    });
  }
  return ctx.session.accessToken;
};

export const getCompetitionById = async ({
  token,
  id,
}: {
  token: string;
  id: string;
}) => {
  const res = await fetch(
    `${env.NEXT_PUBLIC_PTA_ROOT_URL}/api/v2/competitions/${id}`,
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    },
  );
  const json = (await res.json()) as PTACompetition;
  return json;
};

export const getCompetitionByIdCached = unstable_cache(
  async ({ token, id }: { token: string; id: string }) => {
    const res = await fetch(
      `${env.NEXT_PUBLIC_PTA_ROOT_URL}/api/v2/competitions/${id}`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      },
    );
    const json = (await res.json()) as PTACompetition;
    return json;
  },
  [],
  {
    tags: ["competition"],
    revalidate: 120 * 60, // 120 minutes
  },
);

export const getAthletes = unstable_cache(
  async ({ token, ids }: { token: string; ids?: string[] }) => {
    if (!ids || ids.length === 0) return [];
    let url = `${env.NEXT_PUBLIC_PTA_ROOT_URL}/api/v2/athletes?athlete_ids=`;
    ids.forEach((id, index) => {
      url += id;
      if (index !== ids.length - 1) {
        url += ",";
      }
    });

    const res = await fetch(url, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    const json = (await res.json()) as PTAAthlete[];
    return json;
  },
  [],
  {
    tags: ["athletes"],
    revalidate: 120 * 60, // 120 minutes
  },
);

export const callPta = async <T>(
  endpoint: string,
  {
    method,
    body,
    token,
  }: {
    method: "PUT" | "POST" | "GET" | "DELETE";
    body?: string;
    token?: string;
  },
): Promise<T> => {
  try {
    const tokenToUse = token ?? (await getServiceToken()).access_token;
    const url = `${env.NEXT_PUBLIC_PTA_ROOT_URL}api/v2/${endpoint}`;
    console.log("PTA URL", url);
    const res = await fetch(url, {
      method,
      headers: {
        Authorization: `Bearer ${tokenToUse}`,
        "Content-Type": "application/json",
      },
      body,
    });

    if (!res.ok) {
      throw new Error(`HTTP error: status: ${res.status}. ${await res.text()}`);
    }
    const json = (await res.json()) as T;
    return json;
  } catch (error) {
    let message = "Failed to call PTA";
    if (error instanceof Error) {
      message = error.message;
    }
    throw new TRPCError({
      code: "INTERNAL_SERVER_ERROR",
      message,
    });
  }
};
