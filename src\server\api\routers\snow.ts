import { TRPCError } from "@trpc/server";
import { and, eq, inArray, sql } from "drizzle-orm";
import { z } from "zod";
import { Gender } from "~/lib/enums/enums";
import {
  SnowDiscipline,
  SnowEvent,
  SnowFeatureType,
  SnowRailSpinDirection,
  type SnowJumpSpinDirection,
  type SnowLandingType,
  type SnowLandingZone,
} from "~/lib/enums/snow";
import type {
  NewSnowSlopeJumpTag,
  NewSnowSlopeRailTag,
  NewSnowSlopeStyleTag,
  NewSnowSlopeTransition,
  NewSnowSlopeTransitionJumpTag,
  NewSnowSlopeTransitionRailTag,
  NewSnowSlopeTransitionTransitionTag,
} from "~/server/db/snowSchema";
import {
  landingDescriptionEnum,
  snowBigAirTags,
  snowFeatures,
  snowHalfPipeTags,
  snowSlopeJumpTags,
  snowSlopeRailTags,
  snowSlopeStyleTags,
  snowSlopeTransition,
  snowSlopeTransitionJumpTags,
  snowSlopeTransitionRailTags,
  snowSlopeTransitionTransitionTags,
} from "~/server/db/snowSchema";
import type { RouterOutputs } from "~/trpc/react";
import { createTRPCRouter, protectedProcedure } from "../trpc";
import {
  snowTagGeneralInputs,
  snowTagGeneralTagInputs,
} from "../utils/apiInputs";
import { getAccessToken } from "../utils/pta/pta";
import {
  deleteRun,
  getSnowRace,
  getSnowsportRaces,
  upsertFeature,
  upsertRun,
  upsertSnowRace,
} from "../utils/pta/snow";
import { callVideoPortal } from "../utils/video";
import { callPta } from "../utils/pta/pta";
import { PTASnowFeature } from "~/lib/interfaces/snow";

type Outputs = RouterOutputs["snow"];

export type GetRaceOutput = Outputs["getRace"];

export const snowRouter = createTRPCRouter({
  getRacesByCompetitionId: protectedProcedure
    .input(z.object({ competitionId: z.string() }))
    .query(async ({ input, ctx }) => {
      const token = getAccessToken(ctx);
      const races = await getSnowsportRaces({
        token,
        competitionId: input.competitionId,
      });
      return races;
    }),
  updateVideoRace: protectedProcedure
    .input(z.object({ raceId: z.string(), videoId: z.string() }))
    .mutation(async ({ input }) => {
      await callVideoPortal(input.videoId, {
        method: "PUT",
        body: JSON.stringify({ snowSportsRaceId: input.raceId }),
      });
      return input;
    }),
  getRace: protectedProcedure
    .input(
      z.object({
        raceId: z.string(),
      }),
    )
    .query(async ({ input, ctx }) => {
      const token = getAccessToken(ctx);
      const races = await getSnowRace({
        token,
        raceId: input.raceId,
      });
      return races;
    }),
  upsertRace: protectedProcedure
    .input(
      z.object({
        id: z.string().optional(),
        videoId: z.string(),
        competitionId: z.string(),
        round: z.string(),
        date: z.string(),
        gender: z.nativeEnum(Gender),
        event: z.nativeEnum(SnowEvent),
        discipline: z.nativeEnum(SnowDiscipline),
      }),
    )
    .mutation(async ({ input }) => {
      const race = await upsertSnowRace(input);
      await callVideoPortal(input.videoId, {
        method: "PUT",
        body: JSON.stringify({ snowSportsRaceId: race.event_id }),
      });

      return race;
    }),
  upsertRun: protectedProcedure
    .input(
      z.object({
        id: z.string().optional(),
        resultId: z.string(),
        run: z.number(),
        score: z.number().optional(),
        sectionScore: z.number().optional(),
        overallScore: z.number().optional(),
      }),
    )
    .mutation(({ input }) => {
      return upsertRun(input);
    }),
  deleteRun: protectedProcedure
    .input(
      z.object({
        id: z.string(),
      }),
    )
    .mutation(async ({ input }) => {
      await deleteRun({ id: input.id });
    }),
  getTags: protectedProcedure
    .input(
      z.object({
        runId: z.string(),
        event: z.nativeEnum(SnowEvent),
        featureTypeId: z.string().optional(),
        sectionNum: z.number().optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      switch (input.event) {
        case SnowEvent.HALF_PIPE:
          return (
            (await ctx.db.query.snowHalfPipeTags.findFirst({
              where: eq(snowHalfPipeTags.snowSportsRunId, input.runId),
              with: {
                landingDescriptions: true,
              },
            })) ?? null
          );
        case SnowEvent.BIG_AIR:
          return (
            (await ctx.db.query.snowBigAirTags.findFirst({
              where: eq(snowBigAirTags.snowSportsRunId, input.runId),
              with: {
                landingDescriptions: true,
              },
            })) ?? null
          );
        case SnowEvent.SLOPESTYLE:
          const { featureTypeId, sectionNum } = input;
          const andConditions = [
            eq(snowSlopeStyleTags.snowSportsRunId, input.runId),
          ];
          if (featureTypeId) {
            andConditions.push(eq(snowSlopeStyleTags.featureId, featureTypeId));
          }
          if (sectionNum) {
            andConditions.push(eq(snowSlopeStyleTags.sectionNum, sectionNum));
          }

          const slopeStyleTag = await ctx.db.query.snowSlopeStyleTags.findFirst(
            {
              where: and(...andConditions),
              with: {
                landingDescriptions: true,
                snowSlopeRail: true,
                snowSlopeJump: true,
                snowSlopeTransition: {
                  with: {
                    snowSlopeTransitionJump: true,
                    snowSlopeTransitionRail: true,
                    snowSlopeTransitionTransition: true,
                  },
                },
              },
            },
          );
          const transitionTag = slopeStyleTag?.snowSlopeTransition;
          return slopeStyleTag
            ? {
                ...slopeStyleTag,
                ...slopeStyleTag.snowSlopeRail,
                ...slopeStyleTag.snowSlopeJump,
                ...{
                  ...(transitionTag
                    ? {
                        ...transitionTag,
                        ...transitionTag.snowSlopeTransitionJump,
                        ...transitionTag.snowSlopeTransitionRail,
                        ...transitionTag.snowSlopeTransitionTransition,
                      }
                    : {}),
                },
                snowSlopeRail: undefined,
                snowSlopeJump: undefined,
                snowSlopeTransition: undefined,
              }
            : null;
      }
    }),
  getSlopeFeatures: protectedProcedure
    .input(z.object({ raceId: z.string() }))
    .query(({ ctx, input }) => {
      return ctx.db.query.snowFeatures.findMany({
        where: eq(snowFeatures.raceId, input.raceId),
        orderBy: snowFeatures.featureNumber,
      });
    }),
  upsertSlopeFeature: protectedProcedure
    .input(
      z.object({
        upsert: z.array(
          z.object({
            id: z.string().optional(),
            featureNumber: z.number(),
            type: z.nativeEnum(SnowFeatureType),
            sections: z.string().nullable(),
            raceId: z.string(),
          }),
        ),
        delete: z.array(z.string()),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      return await ctx.db.transaction(async (tx) => {
        if (input.delete.length > 0) {
          await tx
            .delete(snowFeatures)
            .where(inArray(snowFeatures.id, input.delete));
        }
        if (input.upsert.length > 0) {
          await tx
            .insert(snowFeatures)
            .values(input.upsert)
            .onDuplicateKeyUpdate({
              set: {
                featureNumber: sql`values(${snowFeatures.featureNumber})`,
                type: sql`values(${snowFeatures.type})`,
                sections: sql`values(${snowFeatures.sections})`,
                raceId: sql`values(${snowFeatures.raceId})`,
              },
            });
        }
      });
    }),
  upsertHalfPipeTag: protectedProcedure
    .input(
      z.object({
        ...snowTagGeneralInputs,
        runScore: z.number().optional(), //pta
        trickNum: z.number(),
        amplitude: z.number().nullish(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { resultId, run, runScore, landingDescriptions, ...rest } = input;

      await ctx.db.transaction(async (tx) => {
        const id = input.id ?? crypto.randomUUID();

        await tx
          .insert(snowHalfPipeTags)
          .values({ ...rest, id })
          .onDuplicateKeyUpdate({
            set: {
              ...rest,
              id: id,
            },
          });
        const ptaFeature: PTASnowFeature = {
          run_id: input.snowSportsRunId,
          trick_num: input.trickNum,
          trick: {
            jump_takeoff_modifiers:
              input.jumpTakeoffModifierId?.toString() ?? "",
            jump_direction: input.spinDirection ?? "",
            jump_spin_type: input.spinTypeId?.toString() ?? "",
            jump_spin_amount: input.spinAmount ?? 0,
            jump_spin_modifiers: input.spinModifierId?.toString() ?? "",
            switch: input.switch ?? false,
            grab_type: input.grabTypeId?.toString() ?? "",
            cab: input.cab ?? false,
            rail_in_spin_direction: "",
            rail_in_spin_amount: 0,
            rail_in_spin_modifiers: "",
            rail_in_grab: "",
            rail_trick: "",
            rail_on_spin_direction: "",
            rail_on_spin_amount: 0,
            rail_on_spin_modifiers: "",
            rail_on_grab: "",
            rail_out_spin_direction: "",
            rail_out_spin_amount: 0,
            rail_out_spin_modifiers: "",
            rail_out_grab: "",
          },
          fis_trick_id: input.executionId?.toString() ?? "",
          feature_num: input.trickNum,
          feature_type: "jump",
          section_type: "jump",
          feature_description: "",
          landing_zone: input.landingZone ?? "",
          landing_type: input.landingType ?? "",
          landing_description: landingDescriptions?.join(", ") ?? "",
          score: runScore ?? 0,
          video_id: input.videoId,
          start_frame: input.startFrame,
          end_frame: input.endFrame,
          take_off_frame: input.takeOffFrame ?? 0,
          landing_frame: input.landingFrame ?? 0,
          air_time: (input.landingFrame ?? 0) - (input.takeOffFrame ?? 0),
          grab_time: (input.grabEnd ?? 0) - (input.grabStart ?? 0),
          progression: input.progression ?? false,
          trick_execution: input.executionId?.toString() ?? "",
        };
        console.log("PTA FEATURE", ptaFeature);
        console.log("INPUT ID", input.id);
        // Call PTA upsertFeature - let it create a new feature since PTA manages its own IDs
        await upsertFeature({
          body: ptaFeature,
        });
        //reset landing description
        await tx
          .delete(landingDescriptionEnum)
          .where(eq(landingDescriptionEnum.halfpipeTagId, id));

        if (landingDescriptions && landingDescriptions.length > 0) {
          await tx.insert(landingDescriptionEnum).values(
            landingDescriptions.map((x) => ({
              halfpipeTagId: id,
              value: x,
            })),
          );
        }

        //update run score in pta
        try {
          await upsertRun({
            id: input.snowSportsRunId,
            resultId,
            run,
            score: runScore,
          });
        } catch (error) {
          // If run doesn't exist, create it without ID first
          console.log("Run update failed, trying to create new run:", error);
          await upsertRun({
            resultId,
            run,
            score: runScore,
          });
        }
      });
      return input;
    }),
  upsertBigAirTag: protectedProcedure
    .input(
      z.object({
        ...snowTagGeneralInputs,
        score: z.number().nullish(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { resultId, run, landingDescriptions, ...rest } = input;

      const id = input.id ?? crypto.randomUUID();

      await ctx.db.transaction(async (tx) => {
        await tx
          .insert(snowBigAirTags)
          .values({ ...rest, id })
          .onDuplicateKeyUpdate({
            set: {
              ...rest,
              id: undefined,
            },
          });
        //reset landing description
        await tx
          .delete(landingDescriptionEnum)
          .where(eq(landingDescriptionEnum.bigAirTagId, id));

        if (landingDescriptions && landingDescriptions.length > 0) {
          await tx.insert(landingDescriptionEnum).values(
            landingDescriptions.map((x) => ({
              bigAirTagId: id,
              value: x,
            })),
          );
        }

        const ptaFeature: PTASnowFeature = {
          run_id: input.snowSportsRunId,
          trick_num: 1,
          trick: {
            jump_takeoff_modifiers:
              input.jumpTakeoffModifierId?.toString() ?? "",
            jump_direction: input.spinDirection ?? "",
            jump_spin_type: input.spinTypeId?.toString() ?? "",
            jump_spin_amount: input.spinAmount ?? 0,
            jump_spin_modifiers: input.spinModifierId?.toString() ?? "",
            switch: input.switch ?? false,
            grab_type: input.grabTypeId?.toString() ?? "",
            cab: input.cab ?? false,
            rail_in_spin_direction: "",
            rail_in_spin_amount: 0,
            rail_in_spin_modifiers: "",
            rail_in_grab: "",
            rail_trick: "",
            rail_on_spin_direction: "",
            rail_on_spin_amount: 0,
            rail_on_spin_modifiers: "",
            rail_on_grab: "",
            rail_out_spin_direction: "",
            rail_out_spin_amount: 0,
            rail_out_spin_modifiers: "",
            rail_out_grab: "",
          },
          fis_trick_id: input.executionId?.toString() ?? "",
          feature_num: 1,
          feature_type: "jump",
          section_type: "jump",
          feature_description: "",
          landing_zone: input.landingZone ?? "",
          landing_type: input.landingType ?? "",
          landing_description: landingDescriptions?.join(", ") ?? "",
          score: input.score ?? 0,
          video_id: input.videoId,
          start_frame: input.startFrame,
          end_frame: input.endFrame,
          take_off_frame: input.takeOffFrame ?? 0,
          landing_frame: input.landingFrame ?? 0,
          air_time: (input.landingFrame ?? 0) - (input.takeOffFrame ?? 0),
          grab_time: (input.grabEnd ?? 0) - (input.grabStart ?? 0),
          progression: input.progression ?? false,
          trick_execution: input.executionId?.toString() ?? "",
        };

        // Call PTA upsertFeature - let it create a new feature since PTA manages its own IDs
        await upsertFeature({
          body: ptaFeature,
        });
        //update run score in pta
        try {
          await upsertRun({
            id: input.snowSportsRunId,
            resultId,
            run,
          });
        } catch (error) {
          // If run doesn't exist, create it without ID first
          console.log("Run update failed, trying to create new run:", error);
          await upsertRun({
            resultId,
            run,
          });
        }
      });
      return input;
    }),
  upsertSlopeStyleTag: protectedProcedure
    .input(
      z.object({
        videoId: z.string(),
        resultId: z.string(), // pta
        raceId: z.string(), // pta
        run: z.number(), //pta
        runScore: z.number().optional(), //pta
        overallScore: z.number().optional(), //pta
        snowSportsRunId: z.string(), // get from pta, write to DB
        tags: z.array(
          z.object({
            ...snowTagGeneralTagInputs,
            score: z.number().nullish(),
            // featureType: z.nativeEnum(SnowFeatureType), //obstacle: jump, rail, transition
            featureId: z.string(),
            sectionNum: z.number(), //section feature number
            sectionType: z.nativeEnum(SnowFeatureType), //section type: jump, rail, transition
            transitionTypeId: z.number().nullish(), //for transition transition only
            //rail fields
            rail: z
              .object({
                railFeatureId: z.number().nullable(),
                railInSpinDirection: z
                  .nativeEnum(SnowRailSpinDirection)
                  .nullable(),
                railInSpinAmount: z.number().nullable(),
                railInSpinModifierId: z.number().nullable(),
                railInGrabId: z.number().nullable(),
                railTrickId: z.number().nullable(),
                railOnSpinDirection: z
                  .nativeEnum(SnowRailSpinDirection)
                  .nullable(),
                railOnSpinAmount: z.number().nullable(),
                railOnSpinModifierId: z.number().nullable(),
                railOnGrabId: z.number().nullable(),
                railOutSpinDirection: z
                  .nativeEnum(SnowRailSpinDirection)
                  .nullable(),
                railOutSpinAmount: z.number().nullable(),
                railOutSpinModifierId: z.number().nullable(),
                railOutGrabId: z.number().nullable(),
              })
              .optional(),
          }),
        ),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { resultId, run, runScore, overallScore } = input;

      const raceFeatures = await ctx.db.query.snowFeatures.findMany({
        where: eq(snowFeatures.raceId, input.raceId),
      });
      await ctx.db.transaction(async (tx) => {
        for (const tag of input.tags) {
          const id = tag.id ?? crypto.randomUUID();

          const parentTag: NewSnowSlopeStyleTag = {
            id,
            startFrame: tag.startFrame,
            endFrame: tag.endFrame,
            featureId: tag.featureId,
            sectionNum: tag.sectionNum,
            sectionType: tag.sectionType,
            snowSportsRunId: input.snowSportsRunId,
            videoId: input.videoId,
            score: tag.score,
          };

          await tx
            .insert(snowSlopeStyleTags)
            .values(parentTag)
            .onDuplicateKeyUpdate({
              set: {
                ...parentTag,
                id: undefined,
              },
            });

          // Create PTA feature body
          const ptaFeature: PTASnowFeature = {
            run_id: input.snowSportsRunId,
            trick_num: tag.sectionNum,
            trick: {
              jump_takeoff_modifiers:
                tag.jumpTakeoffModifierId?.toString() ?? "",
              jump_direction: tag.spinDirection ?? "",
              jump_spin_type: tag.spinTypeId?.toString() ?? "",
              jump_spin_amount: tag.spinAmount ?? 0,
              jump_spin_modifiers: tag.spinModifierId?.toString() ?? "",
              switch: tag.switch ?? false,
              grab_type: tag.grabTypeId?.toString() ?? "",
              cab: tag.cab ?? false,
              rail_in_spin_direction: tag.rail?.railInSpinDirection ?? "",
              rail_in_spin_amount: tag.rail?.railInSpinAmount ?? 0,
              rail_in_spin_modifiers:
                tag.rail?.railInSpinModifierId?.toString() ?? "",
              rail_in_grab: tag.rail?.railInGrabId?.toString() ?? "",
              rail_trick: tag.rail?.railTrickId?.toString() ?? "",
              rail_on_spin_direction: tag.rail?.railOnSpinDirection ?? "",
              rail_on_spin_amount: tag.rail?.railOnSpinAmount ?? 0,
              rail_on_spin_modifiers:
                tag.rail?.railOnSpinModifierId?.toString() ?? "",
              rail_on_grab: tag.rail?.railOnGrabId?.toString() ?? "",
              rail_out_spin_direction: tag.rail?.railOutSpinDirection ?? "",
              rail_out_spin_amount: tag.rail?.railOutSpinAmount ?? 0,
              rail_out_spin_modifiers:
                tag.rail?.railOutSpinModifierId?.toString() ?? "",
              rail_out_grab: tag.rail?.railOutGrabId?.toString() ?? "",
            },
            fis_trick_id: tag.executionId?.toString() ?? "",
            feature_num: tag.sectionNum,
            feature_type: tag.sectionType,
            section_type: tag.sectionType,
            feature_description: "",
            landing_zone: tag.landingZone ?? "",
            landing_type: tag.landingType ?? "",
            landing_description: tag.landingDescriptions?.join(", ") ?? "",
            score: tag.score ?? 0,
            video_id: input.videoId,
            start_frame: tag.startFrame,
            end_frame: tag.endFrame,
            take_off_frame: tag.takeOffFrame ?? 0,
            landing_frame: tag.landingFrame ?? 0,
            air_time: (tag.landingFrame ?? 0) - (tag.takeOffFrame ?? 0),
            grab_time: (tag.grabEnd ?? 0) - (tag.grabStart ?? 0),
            progression: tag.progression ?? false,
            trick_execution: tag.executionId?.toString() ?? "",
          };

          // Call PTA createOrUpdateFeature
          // Use the tag ID as the PTA feature ID, pass it if updating existing tag
          await upsertFeature({
            body: ptaFeature,
          });

          //reset landing description
          await tx
            .delete(landingDescriptionEnum)
            .where(eq(landingDescriptionEnum.slopeTagId, id));

          if (tag.landingDescriptions && tag.landingDescriptions.length > 0) {
            await tx.insert(landingDescriptionEnum).values(
              tag.landingDescriptions.map((x) => ({
                slopeTagId: id,
                value: x,
              })),
            );
          }

          const feature = raceFeatures.find((x) => x.id === tag.featureId);
          if (!feature) {
            throw new TRPCError({
              code: "NOT_FOUND",
              message: `Feature with id ${tag.featureId} not found`,
            });
          }
          const featureType = feature.type;

          switch (featureType) {
            case SnowFeatureType.jump:
              const jumpObj: NewSnowSlopeJumpTag = {
                snowSlopeStyleTagId: id,
                jumpTakeoffModifierId: tag.jumpTakeoffModifierId,
                takeOffFrame: tag.takeOffFrame,
                landingFrame: tag.landingFrame,
                grabStart: tag.grabStart,
                grabEnd: tag.grabEnd,
                jumpTypeId: tag.jumpTypeId,
                spinDirection: tag.spinDirection as SnowJumpSpinDirection,
                spinTypeId: tag.spinTypeId,
                spinAmount: tag.spinAmount,
                spinModifierId: tag.spinModifierId,
                switch: tag.switch,
                cab: tag.cab,
                progression: tag.progression,
                grabTypeId: tag.grabTypeId,
                executionId: tag.executionId,
                landingZone: tag.landingZone as SnowLandingZone,
                landingType: tag.landingType as SnowLandingType,
              };
              await tx
                .insert(snowSlopeJumpTags)
                .values(jumpObj)
                .onDuplicateKeyUpdate({ set: jumpObj });
              break;
            case SnowFeatureType.rail:
              const railObj: NewSnowSlopeRailTag = {
                snowSlopeStyleTagId: id,
                takeOffFrame: tag.takeOffFrame,
                landingFrame: tag.landingFrame,
                grabStart: tag.grabStart,
                grabEnd: tag.grabEnd,
                progression: tag.progression,
                switch: tag.switch,
                cab: tag.cab,
                ...tag.rail,
                executionId: tag.executionId,
                landingZone: tag.landingZone as SnowLandingZone,
                landingType: tag.landingType as SnowLandingType,
              };
              await tx
                .insert(snowSlopeRailTags)
                .values(railObj)
                .onDuplicateKeyUpdate({ set: railObj });
              break;
            case SnowFeatureType.transition:
              const transitionObj: NewSnowSlopeTransition = {
                snowSlopeStyleTagId: id,
                progression: tag.progression,
                switch: tag.switch,
                cab: tag.cab,
                grabStart: tag.grabStart,
                grabEnd: tag.grabEnd,
                takeOffFrame: tag.takeOffFrame,
                landingFrame: tag.landingFrame,
                executionId: tag.executionId,
                landingZone: tag.landingZone as SnowLandingZone,
                landingType: tag.landingType as SnowLandingType,
              };
              await tx
                .insert(snowSlopeTransition)
                .values(transitionObj)
                .onDuplicateKeyUpdate({ set: transitionObj });
              switch (tag.sectionType) {
                case SnowFeatureType.jump:
                  const tJumpObj: NewSnowSlopeTransitionJumpTag = {
                    jumpTakeoffModifierId: tag.jumpTakeoffModifierId,
                    snowSlopeStyleTagId: id,
                    jumpTypeId: tag.jumpTypeId,
                    spinDirection: tag.spinDirection as SnowJumpSpinDirection,
                    spinTypeId: tag.spinTypeId,
                    spinAmount: tag.spinAmount,
                    spinModifierId: tag.spinModifierId,
                    grabTypeId: tag.grabTypeId,
                  };
                  await tx
                    .insert(snowSlopeTransitionJumpTags)
                    .values(tJumpObj)
                    .onDuplicateKeyUpdate({ set: tJumpObj });
                  break;
                case SnowFeatureType.rail:
                  const tRailObj: NewSnowSlopeTransitionRailTag = {
                    snowSlopeStyleTagId: id,
                    ...tag.rail,
                  };
                  await tx
                    .insert(snowSlopeTransitionRailTags)
                    .values(tRailObj)
                    .onDuplicateKeyUpdate({ set: tRailObj });
                  break;
                case SnowFeatureType.transition:
                  if (!tag.transitionTypeId) {
                    throw new TRPCError({
                      code: "BAD_REQUEST",
                      message:
                        "Transition type id is required for transition section",
                    });
                  }
                  const tTransitionObj: NewSnowSlopeTransitionTransitionTag = {
                    snowSlopeStyleTagId: id,
                    jumpTakeoffModifierId: tag.jumpTakeoffModifierId,
                    transitionTypeId: tag.transitionTypeId,
                    spinDirection: tag.spinDirection as SnowJumpSpinDirection,
                    spinTypeId: tag.spinTypeId,
                    spinAmount: tag.spinAmount,
                    spinModifierId: tag.spinModifierId,
                    grabTypeId: tag.grabTypeId,
                  };
                  await tx
                    .insert(snowSlopeTransitionTransitionTags)
                    .values(tTransitionObj)
                    .onDuplicateKeyUpdate({ set: tTransitionObj });
                  break;
              }
              break;
            default:
              break;
          }
        }

        try {
          await upsertRun({
            id: input.snowSportsRunId,
            resultId,
            run,
            score: runScore,
            overallScore,
          });
        } catch (error) {
          // If run doesn't exist, create it without ID first
          console.log("Run update failed, trying to create new run:", error);
          await upsertRun({
            resultId,
            run,
            score: runScore,
            overallScore,
          });
        }
      });
    }),
});
